"use client";
import Link from "next/link";
import React from "react";
import { FiEdit } from 'react-icons/fi';

const Navbar = () => {
  const handleNewConversation = () => {
    window.location.reload();
  };

  const handleHomeClick = (e) => {
    e.preventDefault();
    window.location.reload();
  };

  return (
    <nav className="bg-gray-900 text-white shadow-lg sticky top-0 z-50 ">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link href="/" className="flex-shrink-0" onClick={handleHomeClick}>
            <span className="text-xl sm:text-2xl font-bold text-white hover:text-gray-200 transition-colors duration-200">
              Driply.me
            </span>
          </Link>
          <div className="flex items-center">
            <button
              onClick={handleNewConversation}
              className="   px-4 py-2 rounded-lg cursor-pointer"
            >
              <FiEdit className=" text-white hidden sm:inline text-3xl"/>
               <FiEdit className="md:hidden block text-white  sm:inline text-3xl"/>
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
